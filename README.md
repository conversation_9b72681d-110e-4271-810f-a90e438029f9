# تطبيق البنك الإلكتروني

تطبيق بنك إلكتروني شامل مبني بـ Python و Tkinter يوفر واجهة مستخدم باللغة العربية لإدارة العمليات المصرفية.

## المميزات

### 🏦 الخدمات المصرفية الأساسية
- **💸 التحويلات**: تحويل الأموال بين الحسابات
- **💳 دفع الفواتير**: دفع فواتير الكهرباء والماء والغاز والاتصالات
- **📊 تفاصيل الحساب**: عرض معلومات الحساب والرصيد وآخر المعاملات
- **💰 السحب بدون بطاقة**: إنشاء كود سحب للصرافات الآلية

### 📱 الخدمات الرقمية
- **📱 PAY بنكك**: خدمة الدفع الإلكتروني (قيد التطوير)
- **💎 الودائع الاستثمارية**: عرض أنواع الودائع وعوائدها
- **📋 سجل المعاملات**: عرض جميع المعاملات السابقة في جدول منظم

### 🔧 إدارة الحساب
- **💳 إدارة البطاقات**: حظر/إلغاء حظر البطاقات وتغيير الرقم السري
- **👥 إدارة المستخدمين**: للمديرين فقط
- **🔧 خدمات أخرى**: خدمة العملاء، مواقع الفروع، أسعار الصرف

## متطلبات التشغيل

- Python 3.6 أو أحدث
- مكتبة Tkinter (مدمجة مع Python)
- نظام تشغيل يدعم واجهات Tkinter

## طريقة التشغيل

1. تأكد من تثبيت Python على جهازك
2. شغل الملف مباشرة:
```bash
python bank_app.py
```
أو
```bash
py bank_app.py
```

## البيانات التجريبية

التطبيق يحتوي على حسابات تجريبية للاختبار:

| رقم الحساب | اسم صاحب الحساب | نوع الحساب | الرصيد |
|------------|-----------------|------------|--------|
| *********  | أحمد محمد       | جاري       | 15,000 ريال |
| *********  | فاطمة علي       | توفير      | 25,000 ريال |
| *********  | محمد أحمد       | جاري       | 8,500 ريال |

## الملفات المُنشأة

- `transactions.json`: ملف لحفظ سجل جميع المعاملات

## المميزات التقنية

- **واجهة مستخدم عربية**: جميع النصوص والواجهات باللغة العربية
- **تصميم متجاوب**: ألوان وتأثيرات بصرية جذابة
- **حفظ البيانات**: حفظ المعاملات في ملف JSON
- **التحقق من صحة البيانات**: فحص المدخلات قبل تنفيذ العمليات
- **رسائل تأكيد**: رسائل واضحة لنتائج العمليات

## كيفية الاستخدام

1. **الشاشة الرئيسية**: اختر الخدمة المطلوبة من الأزرار الملونة
2. **التحويلات**: 
   - اختر الحساب المرسل
   - أدخل رقم الحساب المستقبل
   - حدد المبلغ ووصف التحويل
3. **دفع الفواتير**:
   - اختر نوع الفاتورة
   - أدخل رقم الفاتورة والمبلغ
   - اختر الحساب المدين
4. **تفاصيل الحساب**: اختر رقم الحساب لعرض التفاصيل
5. **السحب بدون بطاقة**: أدخل بيانات السحب للحصول على كود

## التطوير المستقبلي

- إضافة قاعدة بيانات حقيقية
- تشفير البيانات الحساسة
- إضافة نظام مصادقة متقدم
- دعم العملات المتعددة
- تطبيق ويب أو موبايل

## الدعم

للمساعدة أو الاستفسارات، يرجى التواصل مع فريق التطوير.

---
**ملاحظة**: هذا تطبيق تعليمي وليس للاستخدام التجاري الفعلي.
