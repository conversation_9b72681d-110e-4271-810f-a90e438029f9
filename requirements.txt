# متطلبات تطبيق البنك الإلكتروني

# مكتبة Tkinter مدمجة مع Python ولا تحتاج تثبيت منفصل
# لكن في حالة عدم توفرها يمكن تثبيتها حسب نظام التشغيل:

# Ubuntu/Debian:
# sudo apt-get install python3-tk

# CentOS/RHEL/Fedora:
# sudo yum install tkinter
# أو
# sudo dnf install python3-tkinter

# Windows:
# Tkinter مدمجة مع Python بشكل افتراضي

# macOS:
# Tkinter مدمجة مع Python بشكل افتراضي
# في حالة عدم التوفر:
# brew install python-tk

# لا توجد مكتبات خارجية مطلوبة
# التطبيق يستخدم فقط المكتبات المدمجة مع Python:
# - tkinter (واجهة المستخدم)
# - json (حفظ البيانات)
# - datetime (التاريخ والوقت)
# - os (عمليات نظام التشغيل)
# - random (توليد أرقام عشوائية)
