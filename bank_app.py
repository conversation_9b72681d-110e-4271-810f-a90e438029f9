import tkinter as tk
from tkinter import messagebox, ttk, simpledialog
from datetime import datetime
import json
import os

class BankApp:
    def __init__(self):
        self.root = tk.Tk()
        self.root.title("تطبيق البنك الإلكتروني")
        self.root.geometry("500x700")
        self.root.configure(bg="#f0f8ff")
        self.root.resizable(False, False)

        # بيانات وهمية للحسابات
        self.accounts = {
            "*********": {"name": "أحمد محمد", "balance": 15000.0, "type": "جاري"},
            "*********": {"name": "فاطمة علي", "balance": 25000.0, "type": "توفير"},
            "*********": {"name": "محمد أحمد", "balance": 8500.0, "type": "جاري"}
        }

        # سجل المعاملات
        self.transactions = []
        self.load_transactions()

        self.setup_main_interface()

    def setup_main_interface(self):
        # إزالة جميع العناصر الموجودة
        for widget in self.root.winfo_children():
            widget.destroy()

        # العنوان الرئيسي
        title_frame = tk.Frame(self.root, bg="#2c3e50", height=80)
        title_frame.pack(fill="x", pady=(0, 20))
        title_frame.pack_propagate(False)

        title_label = tk.Label(title_frame, text="🏦 البنك الإلكتروني",
                              font=("Arial", 18, "bold"),
                              fg="white", bg="#2c3e50")
        title_label.pack(expand=True)

        # إطار الأزرار الرئيسية
        main_frame = tk.Frame(self.root, bg="#f0f8ff")
        main_frame.pack(expand=True, fill="both", padx=20, pady=10)

        # إعداد الخط
        btn_font = ("Arial", 11, "bold")

        # بيانات الأزرار مع الوظائف الحقيقية
        buttons = [
            ("💸 تحويلات", self.open_transfers, "#3498db"),
            ("💳 دفع فواتير", self.open_bill_payment, "#e74c3c"),
            ("📊 تفاصيل الحساب", self.open_account_details, "#2ecc71"),
            ("💰 سحب بدون بطاقة", self.open_cardless_withdrawal, "#f39c12"),
            ("📱 PAY بنكك", self.open_bank_pay, "#9b59b6"),
            ("💎 الودائع الاستثمارية", self.open_investment_deposits, "#1abc9c"),
            ("👥 إدارة المستخدمين", self.open_user_management, "#34495e"),
            ("📋 المعاملات السابقة", self.open_transaction_history, "#16a085"),
            ("💳 إدارة البطاقات", self.open_card_management, "#e67e22"),
            ("🔐 تسجيل الدخول", self.open_login, "#8e44ad"),
            ("⚙️ العمليات", self.open_operations, "#27ae60"),
            ("🔧 خدمات أخرى", self.open_other_services, "#7f8c8d"),
        ]

        # إنشاء الأزرار في شبكة 4×3
        for index, (label, action, color) in enumerate(buttons):
            row = index // 3
            col = index % 3

            btn = tk.Button(main_frame, text=label, font=btn_font,
                           width=18, height=4, command=action,
                           bg=color, fg="white", relief="raised",
                           cursor="hand2", borderwidth=2)
            btn.grid(row=row, column=col, padx=8, pady=8, sticky="nsew")

            # تأثير hover
            btn.bind("<Enter>", lambda e, b=btn, c=color: self.on_hover(b, c))
            btn.bind("<Leave>", lambda e, b=btn, c=color: self.on_leave(b, c))

        # تكوين الشبكة لتكون متجاوبة
        for i in range(3):
            main_frame.columnconfigure(i, weight=1)
        for i in range(4):
            main_frame.rowconfigure(i, weight=1)

        # شريط الحالة
        status_frame = tk.Frame(self.root, bg="#34495e", height=30)
        status_frame.pack(fill="x", side="bottom")
        status_frame.pack_propagate(False)

        status_label = tk.Label(status_frame, text=f"تاريخ اليوم: {datetime.now().strftime('%Y-%m-%d %H:%M')}",
                               font=("Arial", 9), fg="white", bg="#34495e")
        status_label.pack(side="right", padx=10, pady=5)

    def on_hover(self, button, original_color):
        # تغيير لون الزر عند التمرير
        button.configure(bg=self.lighten_color(original_color))

    def on_leave(self, button, original_color):
        # إرجاع اللون الأصلي
        button.configure(bg=original_color)

    def lighten_color(self, color):
        # تفتيح اللون قليلاً
        color_map = {
            "#3498db": "#5dade2",
            "#e74c3c": "#ec7063",
            "#2ecc71": "#58d68d",
            "#f39c12": "#f8c471",
            "#9b59b6": "#bb8fce",
            "#1abc9c": "#48c9b0",
            "#34495e": "#5d6d7e",
            "#16a085": "#45b7aa",
            "#e67e22": "#eb984e",
            "#8e44ad": "#a569bd",
            "#27ae60": "#52c882",
            "#7f8c8d": "#99a3a4"
        }
        return color_map.get(color, color)

    def load_transactions(self):
        """تحميل المعاملات من ملف JSON"""
        try:
            if os.path.exists("transactions.json"):
                with open("transactions.json", "r", encoding="utf-8") as f:
                    self.transactions = json.load(f)
        except:
            self.transactions = []

    def save_transactions(self):
        """حفظ المعاملات في ملف JSON"""
        try:
            with open("transactions.json", "w", encoding="utf-8") as f:
                json.dump(self.transactions, f, ensure_ascii=False, indent=2)
        except:
            pass

    def add_transaction(self, transaction_type, amount, from_account="", to_account="", description=""):
        """إضافة معاملة جديدة"""
        transaction = {
            "id": len(self.transactions) + 1,
            "type": transaction_type,
            "amount": amount,
            "from_account": from_account,
            "to_account": to_account,
            "description": description,
            "date": datetime.now().strftime("%Y-%m-%d %H:%M:%S"),
            "status": "مكتملة"
        }
        self.transactions.append(transaction)
        self.save_transactions()

    def open_transfers(self):
        """فتح نافذة التحويلات"""
        transfer_window = tk.Toplevel(self.root)
        transfer_window.title("التحويلات")
        transfer_window.geometry("400x500")
        transfer_window.configure(bg="#f0f8ff")
        transfer_window.resizable(False, False)

        # العنوان
        title_label = tk.Label(transfer_window, text="💸 تحويل الأموال",
                              font=("Arial", 16, "bold"),
                              fg="#2c3e50", bg="#f0f8ff")
        title_label.pack(pady=20)

        # إطار النموذج
        form_frame = tk.Frame(transfer_window, bg="#ffffff", relief="raised", bd=2)
        form_frame.pack(padx=20, pady=10, fill="both", expand=True)

        # حقول الإدخال
        tk.Label(form_frame, text="من حساب:", font=("Arial", 12), bg="#ffffff").pack(pady=(20, 5))
        from_account_var = tk.StringVar()
        from_account_combo = ttk.Combobox(form_frame, textvariable=from_account_var,
                                         values=list(self.accounts.keys()), width=30)
        from_account_combo.pack(pady=5)

        tk.Label(form_frame, text="إلى حساب:", font=("Arial", 12), bg="#ffffff").pack(pady=(15, 5))
        to_account_var = tk.StringVar()
        to_account_entry = tk.Entry(form_frame, textvariable=to_account_var, width=32, font=("Arial", 11))
        to_account_entry.pack(pady=5)

        tk.Label(form_frame, text="المبلغ:", font=("Arial", 12), bg="#ffffff").pack(pady=(15, 5))
        amount_var = tk.StringVar()
        amount_entry = tk.Entry(form_frame, textvariable=amount_var, width=32, font=("Arial", 11))
        amount_entry.pack(pady=5)

        tk.Label(form_frame, text="وصف التحويل:", font=("Arial", 12), bg="#ffffff").pack(pady=(15, 5))
        description_var = tk.StringVar()
        description_entry = tk.Entry(form_frame, textvariable=description_var, width=32, font=("Arial", 11))
        description_entry.pack(pady=5)

        def execute_transfer():
            try:
                from_acc = from_account_var.get()
                to_acc = to_account_var.get()
                amount = float(amount_var.get())
                desc = description_var.get()

                if not from_acc or not to_acc or amount <= 0:
                    messagebox.showerror("خطأ", "يرجى ملء جميع الحقول بشكل صحيح")
                    return

                if from_acc not in self.accounts:
                    messagebox.showerror("خطأ", "حساب المرسل غير موجود")
                    return

                if self.accounts[from_acc]["balance"] < amount:
                    messagebox.showerror("خطأ", "الرصيد غير كافي")
                    return

                # تنفيذ التحويل
                self.accounts[from_acc]["balance"] -= amount
                if to_acc in self.accounts:
                    self.accounts[to_acc]["balance"] += amount

                # إضافة المعاملة
                self.add_transaction("تحويل", amount, from_acc, to_acc, desc)

                messagebox.showinfo("نجح", f"تم تحويل {amount} ريال بنجاح")
                transfer_window.destroy()

            except ValueError:
                messagebox.showerror("خطأ", "يرجى إدخال مبلغ صحيح")

        # أزرار العمليات
        btn_frame = tk.Frame(form_frame, bg="#ffffff")
        btn_frame.pack(pady=20)

        execute_btn = tk.Button(btn_frame, text="تنفيذ التحويل", command=execute_transfer,
                               bg="#2ecc71", fg="white", font=("Arial", 12, "bold"),
                               width=15, height=2)
        execute_btn.pack(side="left", padx=10)

        cancel_btn = tk.Button(btn_frame, text="إلغاء", command=transfer_window.destroy,
                              bg="#e74c3c", fg="white", font=("Arial", 12, "bold"),
                              width=15, height=2)
        cancel_btn.pack(side="right", padx=10)

    def open_account_details(self):
        """فتح نافذة تفاصيل الحساب"""
        details_window = tk.Toplevel(self.root)
        details_window.title("تفاصيل الحساب")
        details_window.geometry("500x400")
        details_window.configure(bg="#f0f8ff")
        details_window.resizable(False, False)

        # العنوان
        title_label = tk.Label(details_window, text="📊 تفاصيل الحساب",
                              font=("Arial", 16, "bold"),
                              fg="#2c3e50", bg="#f0f8ff")
        title_label.pack(pady=20)

        # اختيار الحساب
        account_frame = tk.Frame(details_window, bg="#f0f8ff")
        account_frame.pack(pady=10)

        tk.Label(account_frame, text="اختر الحساب:", font=("Arial", 12), bg="#f0f8ff").pack(side="left", padx=10)
        account_var = tk.StringVar()
        account_combo = ttk.Combobox(account_frame, textvariable=account_var,
                                    values=list(self.accounts.keys()), width=20)
        account_combo.pack(side="left", padx=10)

        # إطار عرض التفاصيل
        details_frame = tk.Frame(details_window, bg="#ffffff", relief="raised", bd=2)
        details_frame.pack(padx=20, pady=20, fill="both", expand=True)

        details_text = tk.Text(details_frame, font=("Arial", 11), bg="#ffffff",
                              state="disabled", wrap="word")
        details_text.pack(fill="both", expand=True, padx=10, pady=10)

        def show_account_details():
            account_num = account_var.get()
            if account_num in self.accounts:
                account = self.accounts[account_num]
                details_text.config(state="normal")
                details_text.delete(1.0, tk.END)

                details = f"""
رقم الحساب: {account_num}
اسم صاحب الحساب: {account['name']}
نوع الحساب: {account['type']}
الرصيد الحالي: {account['balance']:,.2f} ريال

آخر المعاملات:
"""
                # إضافة آخر 5 معاملات للحساب
                account_transactions = [t for t in self.transactions
                                      if t['from_account'] == account_num or t['to_account'] == account_num]

                for trans in account_transactions[-5:]:
                    trans_type = "خصم" if trans['from_account'] == account_num else "إيداع"
                    details += f"- {trans['date']}: {trans_type} {trans['amount']} ريال - {trans['description']}\n"

                details_text.insert(1.0, details)
                details_text.config(state="disabled")

        show_btn = tk.Button(account_frame, text="عرض التفاصيل", command=show_account_details,
                            bg="#3498db", fg="white", font=("Arial", 10, "bold"))
        show_btn.pack(side="left", padx=10)

    def open_transaction_history(self):
        """فتح نافذة سجل المعاملات"""
        history_window = tk.Toplevel(self.root)
        history_window.title("سجل المعاملات")
        history_window.geometry("700x500")
        history_window.configure(bg="#f0f8ff")

        # العنوان
        title_label = tk.Label(history_window, text="📋 سجل المعاملات",
                              font=("Arial", 16, "bold"),
                              fg="#2c3e50", bg="#f0f8ff")
        title_label.pack(pady=20)

        # إطار الجدول
        table_frame = tk.Frame(history_window, bg="#ffffff", relief="raised", bd=2)
        table_frame.pack(padx=20, pady=10, fill="both", expand=True)

        # إنشاء Treeview للجدول
        columns = ("ID", "النوع", "المبلغ", "من", "إلى", "التاريخ", "الحالة")
        tree = ttk.Treeview(table_frame, columns=columns, show="headings", height=15)

        # تعريف العناوين
        for col in columns:
            tree.heading(col, text=col)
            tree.column(col, width=100, anchor="center")

        # إضافة البيانات
        for trans in reversed(self.transactions):  # أحدث المعاملات أولاً
            tree.insert("", "end", values=(
                trans['id'],
                trans['type'],
                f"{trans['amount']:.2f}",
                trans['from_account'],
                trans['to_account'],
                trans['date'],
                trans['status']
            ))

        # شريط التمرير
        scrollbar = ttk.Scrollbar(table_frame, orient="vertical", command=tree.yview)
        tree.configure(yscrollcommand=scrollbar.set)

        tree.pack(side="left", fill="both", expand=True)
        scrollbar.pack(side="right", fill="y")

    def open_bill_payment(self):
        """فتح نافذة دفع الفواتير"""
        bill_window = tk.Toplevel(self.root)
        bill_window.title("دفع الفواتير")
        bill_window.geometry("400x500")
        bill_window.configure(bg="#f0f8ff")
        bill_window.resizable(False, False)

        # العنوان
        title_label = tk.Label(bill_window, text="💳 دفع الفواتير",
                              font=("Arial", 16, "bold"),
                              fg="#2c3e50", bg="#f0f8ff")
        title_label.pack(pady=20)

        # إطار النموذج
        form_frame = tk.Frame(bill_window, bg="#ffffff", relief="raised", bd=2)
        form_frame.pack(padx=20, pady=10, fill="both", expand=True)

        # نوع الفاتورة
        tk.Label(form_frame, text="نوع الفاتورة:", font=("Arial", 12), bg="#ffffff").pack(pady=(20, 5))
        bill_type_var = tk.StringVar()
        bill_types = ["كهرباء", "ماء", "غاز", "اتصالات", "إنترنت", "أخرى"]
        bill_type_combo = ttk.Combobox(form_frame, textvariable=bill_type_var,
                                      values=bill_types, width=30)
        bill_type_combo.pack(pady=5)

        # رقم الفاتورة
        tk.Label(form_frame, text="رقم الفاتورة:", font=("Arial", 12), bg="#ffffff").pack(pady=(15, 5))
        bill_number_var = tk.StringVar()
        bill_number_entry = tk.Entry(form_frame, textvariable=bill_number_var, width=32, font=("Arial", 11))
        bill_number_entry.pack(pady=5)

        # المبلغ
        tk.Label(form_frame, text="المبلغ:", font=("Arial", 12), bg="#ffffff").pack(pady=(15, 5))
        amount_var = tk.StringVar()
        amount_entry = tk.Entry(form_frame, textvariable=amount_var, width=32, font=("Arial", 11))
        amount_entry.pack(pady=5)

        # الحساب المدين
        tk.Label(form_frame, text="الحساب المدين:", font=("Arial", 12), bg="#ffffff").pack(pady=(15, 5))
        account_var = tk.StringVar()
        account_combo = ttk.Combobox(form_frame, textvariable=account_var,
                                    values=list(self.accounts.keys()), width=30)
        account_combo.pack(pady=5)

        def pay_bill():
            try:
                bill_type = bill_type_var.get()
                bill_number = bill_number_var.get()
                amount = float(amount_var.get())
                account = account_var.get()

                if not all([bill_type, bill_number, amount, account]):
                    messagebox.showerror("خطأ", "يرجى ملء جميع الحقول")
                    return

                if account not in self.accounts:
                    messagebox.showerror("خطأ", "الحساب غير موجود")
                    return

                if self.accounts[account]["balance"] < amount:
                    messagebox.showerror("خطأ", "الرصيد غير كافي")
                    return

                # دفع الفاتورة
                self.accounts[account]["balance"] -= amount
                self.add_transaction("دفع فاتورة", amount, account, "", f"{bill_type} - {bill_number}")

                messagebox.showinfo("نجح", f"تم دفع فاتورة {bill_type} بمبلغ {amount} ريال")
                bill_window.destroy()

            except ValueError:
                messagebox.showerror("خطأ", "يرجى إدخال مبلغ صحيح")

        # أزرار العمليات
        btn_frame = tk.Frame(form_frame, bg="#ffffff")
        btn_frame.pack(pady=20)

        pay_btn = tk.Button(btn_frame, text="دفع الفاتورة", command=pay_bill,
                           bg="#e74c3c", fg="white", font=("Arial", 12, "bold"),
                           width=15, height=2)
        pay_btn.pack(side="left", padx=10)

        cancel_btn = tk.Button(btn_frame, text="إلغاء", command=bill_window.destroy,
                              bg="#95a5a6", fg="white", font=("Arial", 12, "bold"),
                              width=15, height=2)
        cancel_btn.pack(side="right", padx=10)

    def open_cardless_withdrawal(self):
        """فتح نافذة السحب بدون بطاقة"""
        withdrawal_window = tk.Toplevel(self.root)
        withdrawal_window.title("السحب بدون بطاقة")
        withdrawal_window.geometry("400x400")
        withdrawal_window.configure(bg="#f0f8ff")
        withdrawal_window.resizable(False, False)

        # العنوان
        title_label = tk.Label(withdrawal_window, text="💰 السحب بدون بطاقة",
                              font=("Arial", 16, "bold"),
                              fg="#2c3e50", bg="#f0f8ff")
        title_label.pack(pady=20)

        # إطار النموذج
        form_frame = tk.Frame(withdrawal_window, bg="#ffffff", relief="raised", bd=2)
        form_frame.pack(padx=20, pady=10, fill="both", expand=True)

        # رقم الحساب
        tk.Label(form_frame, text="رقم الحساب:", font=("Arial", 12), bg="#ffffff").pack(pady=(20, 5))
        account_var = tk.StringVar()
        account_combo = ttk.Combobox(form_frame, textvariable=account_var,
                                    values=list(self.accounts.keys()), width=30)
        account_combo.pack(pady=5)

        # المبلغ
        tk.Label(form_frame, text="المبلغ:", font=("Arial", 12), bg="#ffffff").pack(pady=(15, 5))
        amount_var = tk.StringVar()
        amount_combo = ttk.Combobox(form_frame, textvariable=amount_var,
                                   values=["100", "200", "500", "1000", "2000"], width=30)
        amount_combo.pack(pady=5)

        # رقم الهاتف
        tk.Label(form_frame, text="رقم الهاتف:", font=("Arial", 12), bg="#ffffff").pack(pady=(15, 5))
        phone_var = tk.StringVar()
        phone_entry = tk.Entry(form_frame, textvariable=phone_var, width=32, font=("Arial", 11))
        phone_entry.pack(pady=5)

        # عرض كود السحب
        code_label = tk.Label(form_frame, text="", font=("Arial", 14, "bold"),
                             fg="#e74c3c", bg="#ffffff")
        code_label.pack(pady=20)

        def generate_withdrawal_code():
            try:
                account = account_var.get()
                amount = float(amount_var.get())
                phone = phone_var.get()

                if not all([account, amount, phone]):
                    messagebox.showerror("خطأ", "يرجى ملء جميع الحقول")
                    return

                if account not in self.accounts:
                    messagebox.showerror("خطأ", "الحساب غير موجود")
                    return

                if self.accounts[account]["balance"] < amount:
                    messagebox.showerror("خطأ", "الرصيد غير كافي")
                    return

                # توليد كود السحب
                import random
                withdrawal_code = str(random.randint(100000, 999999))

                # خصم المبلغ
                self.accounts[account]["balance"] -= amount
                self.add_transaction("سحب بدون بطاقة", amount, account, "", f"كود السحب: {withdrawal_code}")

                code_label.config(text=f"كود السحب: {withdrawal_code}")
                messagebox.showinfo("نجح", f"تم إنشاء كود السحب بنجاح\nالكود: {withdrawal_code}")

            except ValueError:
                messagebox.showerror("خطأ", "يرجى إدخال مبلغ صحيح")

        # زر إنشاء الكود
        generate_btn = tk.Button(form_frame, text="إنشاء كود السحب", command=generate_withdrawal_code,
                                bg="#f39c12", fg="white", font=("Arial", 12, "bold"),
                                width=20, height=2)
        generate_btn.pack(pady=20)

    def open_bank_pay(self):
        """فتح نافذة PAY بنكك"""
        messagebox.showinfo("PAY بنكك", "خدمة الدفع الإلكتروني\nقريباً...")

    def open_investment_deposits(self):
        """فتح نافذة الودائع الاستثمارية"""
        investment_window = tk.Toplevel(self.root)
        investment_window.title("الودائع الاستثمارية")
        investment_window.geometry("500x400")
        investment_window.configure(bg="#f0f8ff")

        # العنوان
        title_label = tk.Label(investment_window, text="💎 الودائع الاستثمارية",
                              font=("Arial", 16, "bold"),
                              fg="#2c3e50", bg="#f0f8ff")
        title_label.pack(pady=20)

        # عرض أنواع الودائع
        deposits_frame = tk.Frame(investment_window, bg="#ffffff", relief="raised", bd=2)
        deposits_frame.pack(padx=20, pady=10, fill="both", expand=True)

        deposits_info = """
أنواع الودائع المتاحة:

🔹 وديعة 3 أشهر - عائد 3.5% سنوياً
🔹 وديعة 6 أشهر - عائد 4.0% سنوياً
🔹 وديعة سنة واحدة - عائد 4.5% سنوياً
🔹 وديعة سنتين - عائد 5.0% سنوياً

الحد الأدنى للإيداع: 10,000 ريال
يمكن كسر الوديعة مع خصم جزء من العائد

للمزيد من المعلومات يرجى زيارة أقرب فرع
        """

        info_label = tk.Label(deposits_frame, text=deposits_info,
                             font=("Arial", 11), bg="#ffffff", justify="right")
        info_label.pack(padx=20, pady=20)

    def open_user_management(self):
        """فتح نافذة إدارة المستخدمين"""
        messagebox.showinfo("إدارة المستخدمين", "هذه الخدمة متاحة للمديرين فقط")

    def open_card_management(self):
        """فتح نافذة إدارة البطاقات"""
        card_window = tk.Toplevel(self.root)
        card_window.title("إدارة البطاقات")
        card_window.geometry("400x300")
        card_window.configure(bg="#f0f8ff")

        # العنوان
        title_label = tk.Label(card_window, text="💳 إدارة البطاقات",
                              font=("Arial", 16, "bold"),
                              fg="#2c3e50", bg="#f0f8ff")
        title_label.pack(pady=20)

        # خيارات البطاقات
        options_frame = tk.Frame(card_window, bg="#ffffff", relief="raised", bd=2)
        options_frame.pack(padx=20, pady=10, fill="both", expand=True)

        def block_card():
            messagebox.showinfo("حظر البطاقة", "تم حظر البطاقة بنجاح")

        def unblock_card():
            messagebox.showinfo("إلغاء حظر البطاقة", "تم إلغاء حظر البطاقة بنجاح")

        def change_pin():
            messagebox.showinfo("تغيير الرقم السري", "تم تغيير الرقم السري بنجاح")

        # أزرار الخيارات
        tk.Button(options_frame, text="حظر البطاقة", command=block_card,
                 bg="#e74c3c", fg="white", font=("Arial", 12, "bold"),
                 width=20, height=2).pack(pady=10)

        tk.Button(options_frame, text="إلغاء حظر البطاقة", command=unblock_card,
                 bg="#2ecc71", fg="white", font=("Arial", 12, "bold"),
                 width=20, height=2).pack(pady=10)

        tk.Button(options_frame, text="تغيير الرقم السري", command=change_pin,
                 bg="#3498db", fg="white", font=("Arial", 12, "bold"),
                 width=20, height=2).pack(pady=10)

    def open_login(self):
        """فتح نافذة تسجيل الدخول"""
        messagebox.showinfo("تسجيل الدخول", "أنت مسجل دخول بالفعل")

    def open_operations(self):
        """فتح نافذة العمليات"""
        messagebox.showinfo("العمليات", "عمليات إضافية قريباً...")

    def open_other_services(self):
        """فتح نافذة الخدمات الأخرى"""
        services_window = tk.Toplevel(self.root)
        services_window.title("خدمات أخرى")
        services_window.geometry("400x300")
        services_window.configure(bg="#f0f8ff")

        # العنوان
        title_label = tk.Label(services_window, text="🔧 خدمات أخرى",
                              font=("Arial", 16, "bold"),
                              fg="#2c3e50", bg="#f0f8ff")
        title_label.pack(pady=20)

        # قائمة الخدمات
        services_frame = tk.Frame(services_window, bg="#ffffff", relief="raised", bd=2)
        services_frame.pack(padx=20, pady=10, fill="both", expand=True)

        services_list = [
            "📞 خدمة العملاء",
            "🏪 مواقع الفروع",
            "💱 أسعار الصرف",
            "📈 الاستثمار",
            "🏠 التمويل العقاري",
            "🚗 تمويل السيارات"
        ]

        for service in services_list:
            tk.Button(services_frame, text=service,
                     bg="#7f8c8d", fg="white", font=("Arial", 11),
                     width=25, height=1,
                     command=lambda s=service: messagebox.showinfo("خدمة", f"{s}\nقريباً...")).pack(pady=5)

    def run(self):
        """تشغيل التطبيق"""
        self.root.mainloop()

# تشغيل التطبيق
if __name__ == "__main__":
    app = BankApp()
    app.run()
