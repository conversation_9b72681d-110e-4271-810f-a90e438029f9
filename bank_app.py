import tkinter as tk
from tkinter import messagebox, ttk, simpledialog
from datetime import datetime
import json
import os

class BankApp:
    def __init__(self):
        self.root = tk.Tk()
        self.root.title("بنك السودان الإلكتروني - الخدمات المصرفية")
        self.root.geometry("500x700")
        self.root.configure(bg="#f0f8ff")
        self.root.resizable(False, False)

        # المستخدم الحالي
        self.current_user = None

        # بيانات وهمية للحسابات - البنوك السودانية
        self.accounts = {
            "*************": {"name": "أحمد محمد علي", "balance": 150000.0, "type": "جاري", "phone": "**********", "status": "نشط"},
            "*************": {"name": "فاطمة عبدالله أحمد", "balance": 250000.0, "type": "توفير", "phone": "**********", "status": "نشط"},
            "*************": {"name": "محمد عثمان إبراهيم", "balance": 85000.0, "type": "جاري", "phone": "**********", "status": "نشط"},
            "*************": {"name": "عائشة محمد حسن", "balance": 320000.0, "type": "استثماري", "phone": "**********", "status": "نشط"}
        }

        # بيانات المستخدمين للنظام
        self.users = {
            "admin": {"password": "admin123", "role": "مدير", "name": "مدير النظام"},
            "teller": {"password": "teller123", "role": "موظف", "name": "موظف الخدمات"},
            "customer": {"password": "customer123", "role": "عميل", "name": "عميل البنك"}
        }

        self.current_user = None

        # خدمات الدفع الإلكتروني
        self.payment_services = {
            "فيزا": {"fee": 5.0, "status": "متاح"},
            "ماستركارد": {"fee": 5.0, "status": "متاح"},
            "بنكك": {"fee": 2.0, "status": "متاح"},
            "سداد": {"fee": 3.0, "status": "متاح"},
            "موبي كاش": {"fee": 1.0, "status": "متاح"},
            "زين كاش": {"fee": 1.0, "status": "متاح"}
        }

        # سجل المعاملات
        self.transactions = []
        self.load_transactions()

        self.setup_main_interface()

    def setup_main_interface(self):
        # إزالة جميع العناصر الموجودة
        for widget in self.root.winfo_children():
            widget.destroy()

        # العنوان الرئيسي
        title_frame = tk.Frame(self.root, bg="#2c3e50", height=80)
        title_frame.pack(fill="x", pady=(0, 20))
        title_frame.pack_propagate(False)

        title_label = tk.Label(title_frame, text="🏦 بنك السودان الإلكتروني",
                              font=("Arial", 18, "bold"),
                              fg="white", bg="#2c3e50")
        title_label.pack(expand=True)

        # إطار الأزرار الرئيسية
        main_frame = tk.Frame(self.root, bg="#f0f8ff")
        main_frame.pack(expand=True, fill="both", padx=20, pady=10)

        # إعداد الخط
        btn_font = ("Arial", 11, "bold")

        # بيانات الأزرار مع الوظائف الحقيقية
        buttons = [
            ("💸 تحويلات", self.open_transfers, "#3498db"),
            ("💳 دفع فواتير", self.open_bill_payment, "#e74c3c"),
            ("📊 تفاصيل الحساب", self.open_account_details, "#2ecc71"),
            ("💰 سحب بدون بطاقة", self.open_cardless_withdrawal, "#f39c12"),
            ("📱 PAY بنكك", self.open_bank_pay, "#9b59b6"),
            ("💎 الودائع الاستثمارية", self.open_investment_deposits, "#1abc9c"),
            ("👥 إدارة المستخدمين", self.open_user_management, "#34495e"),
            ("📋 المعاملات السابقة", self.open_transaction_history, "#16a085"),
            ("💳 إدارة البطاقات", self.open_card_management, "#e67e22"),
            ("🔐 تسجيل الدخول", self.open_login, "#8e44ad"),
            ("⚙️ العمليات", self.open_operations, "#27ae60"),
            ("🔧 خدمات أخرى", self.open_other_services, "#7f8c8d"),
        ]

        # إنشاء الأزرار في شبكة 4×3
        for index, (label, action, color) in enumerate(buttons):
            row = index // 3
            col = index % 3

            btn = tk.Button(main_frame, text=label, font=btn_font,
                           width=18, height=4, command=action,
                           bg=color, fg="white", relief="raised",
                           cursor="hand2", borderwidth=2)
            btn.grid(row=row, column=col, padx=8, pady=8, sticky="nsew")

            # تأثير hover
            btn.bind("<Enter>", lambda e, b=btn, c=color: self.on_hover(b, c))
            btn.bind("<Leave>", lambda e, b=btn, c=color: self.on_leave(b, c))

        # تكوين الشبكة لتكون متجاوبة
        for i in range(3):
            main_frame.columnconfigure(i, weight=1)
        for i in range(4):
            main_frame.rowconfigure(i, weight=1)

        # شريط الحالة
        status_frame = tk.Frame(self.root, bg="#34495e", height=30)
        status_frame.pack(fill="x", side="bottom")
        status_frame.pack_propagate(False)

        status_label = tk.Label(status_frame, text=f"تاريخ اليوم: {datetime.now().strftime('%Y-%m-%d %H:%M')}",
                               font=("Arial", 9), fg="white", bg="#34495e")
        status_label.pack(side="right", padx=10, pady=5)

    def on_hover(self, button, original_color):
        # تغيير لون الزر عند التمرير
        button.configure(bg=self.lighten_color(original_color))

    def on_leave(self, button, original_color):
        # إرجاع اللون الأصلي
        button.configure(bg=original_color)

    def lighten_color(self, color):
        # تفتيح اللون قليلاً
        color_map = {
            "#3498db": "#5dade2",
            "#e74c3c": "#ec7063",
            "#2ecc71": "#58d68d",
            "#f39c12": "#f8c471",
            "#9b59b6": "#bb8fce",
            "#1abc9c": "#48c9b0",
            "#34495e": "#5d6d7e",
            "#16a085": "#45b7aa",
            "#e67e22": "#eb984e",
            "#8e44ad": "#a569bd",
            "#27ae60": "#52c882",
            "#7f8c8d": "#99a3a4"
        }
        return color_map.get(color, color)

    def load_transactions(self):
        """تحميل المعاملات من ملف JSON"""
        try:
            if os.path.exists("transactions.json"):
                with open("transactions.json", "r", encoding="utf-8") as f:
                    self.transactions = json.load(f)
        except:
            self.transactions = []

    def save_transactions(self):
        """حفظ المعاملات في ملف JSON"""
        try:
            with open("transactions.json", "w", encoding="utf-8") as f:
                json.dump(self.transactions, f, ensure_ascii=False, indent=2)
        except:
            pass

    def add_transaction(self, transaction_type, amount, from_account="", to_account="", description=""):
        """إضافة معاملة جديدة"""
        transaction = {
            "id": len(self.transactions) + 1,
            "type": transaction_type,
            "amount": amount,
            "from_account": from_account,
            "to_account": to_account,
            "description": description,
            "date": datetime.now().strftime("%Y-%m-%d %H:%M:%S"),
            "status": "مكتملة"
        }
        self.transactions.append(transaction)
        self.save_transactions()

    def open_transfers(self):
        """فتح نافذة التحويلات"""
        transfer_window = tk.Toplevel(self.root)
        transfer_window.title("التحويلات")
        transfer_window.geometry("400x500")
        transfer_window.configure(bg="#f0f8ff")
        transfer_window.resizable(False, False)

        # العنوان
        title_label = tk.Label(transfer_window, text="💸 تحويل الأموال",
                              font=("Arial", 16, "bold"),
                              fg="#2c3e50", bg="#f0f8ff")
        title_label.pack(pady=20)

        # إطار النموذج
        form_frame = tk.Frame(transfer_window, bg="#ffffff", relief="raised", bd=2)
        form_frame.pack(padx=20, pady=10, fill="both", expand=True)

        # حقول الإدخال
        tk.Label(form_frame, text="من حساب:", font=("Arial", 12), bg="#ffffff").pack(pady=(20, 5))
        from_account_var = tk.StringVar()
        from_account_combo = ttk.Combobox(form_frame, textvariable=from_account_var,
                                         values=list(self.accounts.keys()), width=30)
        from_account_combo.pack(pady=5)

        tk.Label(form_frame, text="إلى حساب:", font=("Arial", 12), bg="#ffffff").pack(pady=(15, 5))
        to_account_var = tk.StringVar()
        to_account_entry = tk.Entry(form_frame, textvariable=to_account_var, width=32, font=("Arial", 11))
        to_account_entry.pack(pady=5)

        tk.Label(form_frame, text="المبلغ:", font=("Arial", 12), bg="#ffffff").pack(pady=(15, 5))
        amount_var = tk.StringVar()
        amount_entry = tk.Entry(form_frame, textvariable=amount_var, width=32, font=("Arial", 11))
        amount_entry.pack(pady=5)

        tk.Label(form_frame, text="وصف التحويل:", font=("Arial", 12), bg="#ffffff").pack(pady=(15, 5))
        description_var = tk.StringVar()
        description_entry = tk.Entry(form_frame, textvariable=description_var, width=32, font=("Arial", 11))
        description_entry.pack(pady=5)

        def execute_transfer():
            try:
                from_acc = from_account_var.get()
                to_acc = to_account_var.get()
                amount = float(amount_var.get())
                desc = description_var.get()

                if not from_acc or not to_acc or amount <= 0:
                    messagebox.showerror("خطأ", "يرجى ملء جميع الحقول بشكل صحيح")
                    return

                if from_acc not in self.accounts:
                    messagebox.showerror("خطأ", "حساب المرسل غير موجود")
                    return

                if self.accounts[from_acc]["balance"] < amount:
                    messagebox.showerror("خطأ", "الرصيد غير كافي")
                    return

                # تنفيذ التحويل
                self.accounts[from_acc]["balance"] -= amount
                if to_acc in self.accounts:
                    self.accounts[to_acc]["balance"] += amount

                # إضافة المعاملة
                self.add_transaction("تحويل", amount, from_acc, to_acc, desc)

                messagebox.showinfo("نجح", f"تم تحويل {amount:,.0f} جنيه سوداني بنجاح")
                transfer_window.destroy()

            except ValueError:
                messagebox.showerror("خطأ", "يرجى إدخال مبلغ صحيح")

        # أزرار العمليات
        btn_frame = tk.Frame(form_frame, bg="#ffffff")
        btn_frame.pack(pady=20)

        execute_btn = tk.Button(btn_frame, text="تنفيذ التحويل", command=execute_transfer,
                               bg="#2ecc71", fg="white", font=("Arial", 12, "bold"),
                               width=15, height=2)
        execute_btn.pack(side="left", padx=10)

        cancel_btn = tk.Button(btn_frame, text="إلغاء", command=transfer_window.destroy,
                              bg="#e74c3c", fg="white", font=("Arial", 12, "bold"),
                              width=15, height=2)
        cancel_btn.pack(side="right", padx=10)

    def open_account_details(self):
        """فتح نافذة تفاصيل الحساب"""
        details_window = tk.Toplevel(self.root)
        details_window.title("تفاصيل الحساب")
        details_window.geometry("500x400")
        details_window.configure(bg="#f0f8ff")
        details_window.resizable(False, False)

        # العنوان
        title_label = tk.Label(details_window, text="📊 تفاصيل الحساب",
                              font=("Arial", 16, "bold"),
                              fg="#2c3e50", bg="#f0f8ff")
        title_label.pack(pady=20)

        # اختيار الحساب
        account_frame = tk.Frame(details_window, bg="#f0f8ff")
        account_frame.pack(pady=10)

        tk.Label(account_frame, text="اختر الحساب:", font=("Arial", 12), bg="#f0f8ff").pack(side="left", padx=10)
        account_var = tk.StringVar()
        account_combo = ttk.Combobox(account_frame, textvariable=account_var,
                                    values=list(self.accounts.keys()), width=20)
        account_combo.pack(side="left", padx=10)

        # إطار عرض التفاصيل
        details_frame = tk.Frame(details_window, bg="#ffffff", relief="raised", bd=2)
        details_frame.pack(padx=20, pady=20, fill="both", expand=True)

        details_text = tk.Text(details_frame, font=("Arial", 11), bg="#ffffff",
                              state="disabled", wrap="word")
        details_text.pack(fill="both", expand=True, padx=10, pady=10)

        def show_account_details():
            account_num = account_var.get()
            if account_num in self.accounts:
                account = self.accounts[account_num]
                details_text.config(state="normal")
                details_text.delete(1.0, tk.END)

                details = f"""
رقم الحساب: {account_num}
اسم صاحب الحساب: {account['name']}
نوع الحساب: {account['type']}
الرصيد الحالي: {account['balance']:,.0f} جنيه سوداني

آخر المعاملات:
"""
                # إضافة آخر 5 معاملات للحساب
                account_transactions = [t for t in self.transactions
                                      if t['from_account'] == account_num or t['to_account'] == account_num]

                for trans in account_transactions[-5:]:
                    trans_type = "خصم" if trans['from_account'] == account_num else "إيداع"
                    details += f"- {trans['date']}: {trans_type} {trans['amount']:,.0f} جنيه سوداني - {trans['description']}\n"

                details_text.insert(1.0, details)
                details_text.config(state="disabled")

        show_btn = tk.Button(account_frame, text="عرض التفاصيل", command=show_account_details,
                            bg="#3498db", fg="white", font=("Arial", 10, "bold"))
        show_btn.pack(side="left", padx=10)

    def open_transaction_history(self):
        """فتح نافذة سجل المعاملات"""
        history_window = tk.Toplevel(self.root)
        history_window.title("سجل المعاملات")
        history_window.geometry("700x500")
        history_window.configure(bg="#f0f8ff")

        # العنوان
        title_label = tk.Label(history_window, text="📋 سجل المعاملات",
                              font=("Arial", 16, "bold"),
                              fg="#2c3e50", bg="#f0f8ff")
        title_label.pack(pady=20)

        # إطار الجدول
        table_frame = tk.Frame(history_window, bg="#ffffff", relief="raised", bd=2)
        table_frame.pack(padx=20, pady=10, fill="both", expand=True)

        # إنشاء Treeview للجدول
        columns = ("ID", "النوع", "المبلغ", "من", "إلى", "التاريخ", "الحالة")
        tree = ttk.Treeview(table_frame, columns=columns, show="headings", height=15)

        # تعريف العناوين
        for col in columns:
            tree.heading(col, text=col)
            tree.column(col, width=100, anchor="center")

        # إضافة البيانات
        for trans in reversed(self.transactions):  # أحدث المعاملات أولاً
            tree.insert("", "end", values=(
                trans['id'],
                trans['type'],
                f"{trans['amount']:,.0f} جنيه",
                trans['from_account'],
                trans['to_account'],
                trans['date'],
                trans['status']
            ))

        # شريط التمرير
        scrollbar = ttk.Scrollbar(table_frame, orient="vertical", command=tree.yview)
        tree.configure(yscrollcommand=scrollbar.set)

        tree.pack(side="left", fill="both", expand=True)
        scrollbar.pack(side="right", fill="y")

    def open_bill_payment(self):
        """فتح نافذة دفع الفواتير"""
        bill_window = tk.Toplevel(self.root)
        bill_window.title("دفع الفواتير")
        bill_window.geometry("400x500")
        bill_window.configure(bg="#f0f8ff")
        bill_window.resizable(False, False)

        # العنوان
        title_label = tk.Label(bill_window, text="💳 دفع الفواتير",
                              font=("Arial", 16, "bold"),
                              fg="#2c3e50", bg="#f0f8ff")
        title_label.pack(pady=20)

        # إطار النموذج
        form_frame = tk.Frame(bill_window, bg="#ffffff", relief="raised", bd=2)
        form_frame.pack(padx=20, pady=10, fill="both", expand=True)

        # نوع الفاتورة
        tk.Label(form_frame, text="نوع الفاتورة:", font=("Arial", 12), bg="#ffffff").pack(pady=(20, 5))
        bill_type_var = tk.StringVar()
        bill_types = ["كهرباء - الشركة السودانية", "مياه - مؤسسة المياه", "اتصالات - سوداني",
                     "اتصالات - زين", "اتصالات - MTN", "إنترنت - كنار", "جامعات", "أخرى"]
        bill_type_combo = ttk.Combobox(form_frame, textvariable=bill_type_var,
                                      values=bill_types, width=30)
        bill_type_combo.pack(pady=5)

        # رقم الفاتورة
        tk.Label(form_frame, text="رقم الفاتورة:", font=("Arial", 12), bg="#ffffff").pack(pady=(15, 5))
        bill_number_var = tk.StringVar()
        bill_number_entry = tk.Entry(form_frame, textvariable=bill_number_var, width=32, font=("Arial", 11))
        bill_number_entry.pack(pady=5)

        # المبلغ
        tk.Label(form_frame, text="المبلغ:", font=("Arial", 12), bg="#ffffff").pack(pady=(15, 5))
        amount_var = tk.StringVar()
        amount_entry = tk.Entry(form_frame, textvariable=amount_var, width=32, font=("Arial", 11))
        amount_entry.pack(pady=5)

        # الحساب المدين
        tk.Label(form_frame, text="الحساب المدين:", font=("Arial", 12), bg="#ffffff").pack(pady=(15, 5))
        account_var = tk.StringVar()
        account_combo = ttk.Combobox(form_frame, textvariable=account_var,
                                    values=list(self.accounts.keys()), width=30)
        account_combo.pack(pady=5)

        def pay_bill():
            try:
                bill_type = bill_type_var.get()
                bill_number = bill_number_var.get()
                amount = float(amount_var.get())
                account = account_var.get()

                if not all([bill_type, bill_number, amount, account]):
                    messagebox.showerror("خطأ", "يرجى ملء جميع الحقول")
                    return

                if account not in self.accounts:
                    messagebox.showerror("خطأ", "الحساب غير موجود")
                    return

                if self.accounts[account]["balance"] < amount:
                    messagebox.showerror("خطأ", "الرصيد غير كافي")
                    return

                # دفع الفاتورة
                self.accounts[account]["balance"] -= amount
                self.add_transaction("دفع فاتورة", amount, account, "", f"{bill_type} - {bill_number}")

                messagebox.showinfo("نجح", f"تم دفع فاتورة {bill_type} بمبلغ {amount:,.0f} جنيه سوداني")
                bill_window.destroy()

            except ValueError:
                messagebox.showerror("خطأ", "يرجى إدخال مبلغ صحيح")

        # أزرار العمليات
        btn_frame = tk.Frame(form_frame, bg="#ffffff")
        btn_frame.pack(pady=20)

        pay_btn = tk.Button(btn_frame, text="دفع الفاتورة", command=pay_bill,
                           bg="#e74c3c", fg="white", font=("Arial", 12, "bold"),
                           width=15, height=2)
        pay_btn.pack(side="left", padx=10)

        cancel_btn = tk.Button(btn_frame, text="إلغاء", command=bill_window.destroy,
                              bg="#95a5a6", fg="white", font=("Arial", 12, "bold"),
                              width=15, height=2)
        cancel_btn.pack(side="right", padx=10)

    def open_cardless_withdrawal(self):
        """فتح نافذة السحب بدون بطاقة"""
        withdrawal_window = tk.Toplevel(self.root)
        withdrawal_window.title("السحب بدون بطاقة")
        withdrawal_window.geometry("400x400")
        withdrawal_window.configure(bg="#f0f8ff")
        withdrawal_window.resizable(False, False)

        # العنوان
        title_label = tk.Label(withdrawal_window, text="💰 السحب بدون بطاقة",
                              font=("Arial", 16, "bold"),
                              fg="#2c3e50", bg="#f0f8ff")
        title_label.pack(pady=20)

        # إطار النموذج
        form_frame = tk.Frame(withdrawal_window, bg="#ffffff", relief="raised", bd=2)
        form_frame.pack(padx=20, pady=10, fill="both", expand=True)

        # رقم الحساب
        tk.Label(form_frame, text="رقم الحساب:", font=("Arial", 12), bg="#ffffff").pack(pady=(20, 5))
        account_var = tk.StringVar()
        account_combo = ttk.Combobox(form_frame, textvariable=account_var,
                                    values=list(self.accounts.keys()), width=30)
        account_combo.pack(pady=5)

        # المبلغ
        tk.Label(form_frame, text="المبلغ:", font=("Arial", 12), bg="#ffffff").pack(pady=(15, 5))
        amount_var = tk.StringVar()
        amount_combo = ttk.Combobox(form_frame, textvariable=amount_var,
                                   values=["1000", "2000", "5000", "10000", "20000", "50000"], width=30)
        amount_combo.pack(pady=5)

        # رقم الهاتف
        tk.Label(form_frame, text="رقم الهاتف:", font=("Arial", 12), bg="#ffffff").pack(pady=(15, 5))
        phone_var = tk.StringVar()
        phone_entry = tk.Entry(form_frame, textvariable=phone_var, width=32, font=("Arial", 11))
        phone_entry.pack(pady=5)

        # عرض كود السحب
        code_label = tk.Label(form_frame, text="", font=("Arial", 14, "bold"),
                             fg="#e74c3c", bg="#ffffff")
        code_label.pack(pady=20)

        def generate_withdrawal_code():
            try:
                account = account_var.get()
                amount = float(amount_var.get())
                phone = phone_var.get()

                if not all([account, amount, phone]):
                    messagebox.showerror("خطأ", "يرجى ملء جميع الحقول")
                    return

                if account not in self.accounts:
                    messagebox.showerror("خطأ", "الحساب غير موجود")
                    return

                if self.accounts[account]["balance"] < amount:
                    messagebox.showerror("خطأ", "الرصيد غير كافي")
                    return

                # توليد كود السحب
                import random
                withdrawal_code = str(random.randint(100000, 999999))

                # خصم المبلغ
                self.accounts[account]["balance"] -= amount
                self.add_transaction("سحب بدون بطاقة", amount, account, "", f"كود السحب: {withdrawal_code}")

                code_label.config(text=f"كود السحب: {withdrawal_code}")
                messagebox.showinfo("نجح", f"تم إنشاء كود السحب بنجاح\nالكود: {withdrawal_code}")

            except ValueError:
                messagebox.showerror("خطأ", "يرجى إدخال مبلغ صحيح")

        # زر إنشاء الكود
        generate_btn = tk.Button(form_frame, text="إنشاء كود السحب", command=generate_withdrawal_code,
                                bg="#f39c12", fg="white", font=("Arial", 12, "bold"),
                                width=20, height=2)
        generate_btn.pack(pady=20)

    def open_bank_pay(self):
        """فتح نافذة خدمات الدفع الإلكتروني"""
        pay_window = tk.Toplevel(self.root)
        pay_window.title("خدمات الدفع الإلكتروني")
        pay_window.geometry("500x600")
        pay_window.configure(bg="#f0f8ff")
        pay_window.resizable(False, False)

        # العنوان
        title_label = tk.Label(pay_window, text="📱 خدمات الدفع الإلكتروني",
                              font=("Arial", 16, "bold"),
                              fg="#2c3e50", bg="#f0f8ff")
        title_label.pack(pady=20)

        # إطار الخدمات
        services_frame = tk.Frame(pay_window, bg="#ffffff", relief="raised", bd=2)
        services_frame.pack(padx=20, pady=10, fill="both", expand=True)

        # عنوان الخدمات
        tk.Label(services_frame, text="اختر خدمة الدفع:",
                font=("Arial", 14, "bold"), bg="#ffffff", fg="#2c3e50").pack(pady=15)

        # أزرار الخدمات
        services = [
            ("💳 بنكك - Bankak", "#3498db", lambda: self.bankak_payment()),
            ("📱 سداد - Sadad", "#e74c3c", lambda: self.sadad_payment()),
            ("💰 موبي كاش - Mobi Cash", "#2ecc71", lambda: self.mobi_cash_payment()),
            ("📞 زين كاش - Zain Cash", "#f39c12", lambda: self.zain_cash_payment()),
            ("💎 فيزا/ماستركارد", "#9b59b6", lambda: self.card_payment()),
            ("🏪 دفع للتجار", "#1abc9c", lambda: self.merchant_payment())
        ]

        for service_name, color, command in services:
            btn = tk.Button(services_frame, text=service_name,
                           bg=color, fg="white", font=("Arial", 12, "bold"),
                           width=30, height=2, command=command)
            btn.pack(pady=8, padx=20)

        # معلومات الرسوم
        fees_frame = tk.Frame(services_frame, bg="#ecf0f1", relief="sunken", bd=1)
        fees_frame.pack(fill="x", padx=20, pady=20)

        fees_title = tk.Label(fees_frame, text="رسوم الخدمات:",
                             font=("Arial", 12, "bold"), bg="#ecf0f1")
        fees_title.pack(pady=5)

        fees_text = """
• بنكك: 2 جنيه سوداني
• سداد: 3 جنيه سوداني
• موبي كاش: 1 جنيه سوداني
• زين كاش: 1 جنيه سوداني
• فيزا/ماستركارد: 5 جنيه سوداني
        """

        fees_label = tk.Label(fees_frame, text=fees_text,
                             font=("Arial", 10), bg="#ecf0f1", justify="right")
        fees_label.pack(pady=5)

    def bankak_payment(self):
        """دفع عبر بنكك"""
        self.electronic_payment_form("بنكك", 2.0)

    def sadad_payment(self):
        """دفع عبر سداد"""
        self.electronic_payment_form("سداد", 3.0)

    def mobi_cash_payment(self):
        """دفع عبر موبي كاش"""
        self.electronic_payment_form("موبي كاش", 1.0)

    def zain_cash_payment(self):
        """دفع عبر زين كاش"""
        self.electronic_payment_form("زين كاش", 1.0)

    def card_payment(self):
        """دفع بالبطاقة"""
        self.electronic_payment_form("فيزا/ماستركارد", 5.0)

    def merchant_payment(self):
        """دفع للتجار"""
        self.electronic_payment_form("دفع للتجار", 0.0)

    def electronic_payment_form(self, service_name, fee):
        """نموذج الدفع الإلكتروني"""
        payment_window = tk.Toplevel(self.root)
        payment_window.title(f"دفع عبر {service_name}")
        payment_window.geometry("450x500")
        payment_window.configure(bg="#f0f8ff")
        payment_window.resizable(False, False)

        # العنوان
        title_label = tk.Label(payment_window, text=f"💳 دفع عبر {service_name}",
                              font=("Arial", 16, "bold"),
                              fg="#2c3e50", bg="#f0f8ff")
        title_label.pack(pady=20)

        # إطار النموذج
        form_frame = tk.Frame(payment_window, bg="#ffffff", relief="raised", bd=2)
        form_frame.pack(padx=20, pady=10, fill="both", expand=True)

        # الحساب المدين
        tk.Label(form_frame, text="الحساب المدين:", font=("Arial", 12), bg="#ffffff").pack(pady=(20, 5))
        account_var = tk.StringVar()
        account_combo = ttk.Combobox(form_frame, textvariable=account_var,
                                    values=list(self.accounts.keys()), width=30)
        account_combo.pack(pady=5)

        # رقم الهاتف أو البطاقة
        if service_name in ["موبي كاش", "زين كاش"]:
            label_text = "رقم الهاتف:"
        elif service_name in ["فيزا/ماستركارد"]:
            label_text = "رقم البطاقة:"
        else:
            label_text = "رقم المستفيد:"

        tk.Label(form_frame, text=label_text, font=("Arial", 12), bg="#ffffff").pack(pady=(15, 5))
        recipient_var = tk.StringVar()
        recipient_entry = tk.Entry(form_frame, textvariable=recipient_var, width=32, font=("Arial", 11))
        recipient_entry.pack(pady=5)

        # المبلغ
        tk.Label(form_frame, text="المبلغ:", font=("Arial", 12), bg="#ffffff").pack(pady=(15, 5))
        amount_var = tk.StringVar()
        amount_entry = tk.Entry(form_frame, textvariable=amount_var, width=32, font=("Arial", 11))
        amount_entry.pack(pady=5)

        # الوصف
        tk.Label(form_frame, text="وصف العملية:", font=("Arial", 12), bg="#ffffff").pack(pady=(15, 5))
        description_var = tk.StringVar()
        description_entry = tk.Entry(form_frame, textvariable=description_var, width=32, font=("Arial", 11))
        description_entry.pack(pady=5)

        # عرض الرسوم
        fee_label = tk.Label(form_frame, text=f"رسوم الخدمة: {fee:,.0f} جنيه سوداني",
                            font=("Arial", 11, "bold"), fg="#e74c3c", bg="#ffffff")
        fee_label.pack(pady=15)

        def execute_payment():
            try:
                account = account_var.get()
                recipient = recipient_var.get()
                amount = float(amount_var.get())
                description = description_var.get()
                total_amount = amount + fee

                if not all([account, recipient, amount]):
                    messagebox.showerror("خطأ", "يرجى ملء جميع الحقول المطلوبة")
                    return

                if account not in self.accounts:
                    messagebox.showerror("خطأ", "الحساب غير موجود")
                    return

                if self.accounts[account]["balance"] < total_amount:
                    messagebox.showerror("خطأ", f"الرصيد غير كافي\nالمبلغ المطلوب: {total_amount:,.0f} جنيه سوداني")
                    return

                # تنفيذ الدفع
                self.accounts[account]["balance"] -= total_amount
                self.add_transaction(f"دفع إلكتروني - {service_name}", amount, account, recipient,
                                   f"{description} (رسوم: {fee:,.0f} جنيه)")

                messagebox.showinfo("نجح",
                    f"تم الدفع بنجاح عبر {service_name}\n"
                    f"المبلغ: {amount:,.0f} جنيه سوداني\n"
                    f"الرسوم: {fee:,.0f} جنيه سوداني\n"
                    f"المجموع: {total_amount:,.0f} جنيه سوداني")
                payment_window.destroy()

            except ValueError:
                messagebox.showerror("خطأ", "يرجى إدخال مبلغ صحيح")

        # أزرار العمليات
        btn_frame = tk.Frame(form_frame, bg="#ffffff")
        btn_frame.pack(pady=20)

        execute_btn = tk.Button(btn_frame, text="تنفيذ الدفع", command=execute_payment,
                               bg="#2ecc71", fg="white", font=("Arial", 12, "bold"),
                               width=15, height=2)
        execute_btn.pack(side="left", padx=10)

        cancel_btn = tk.Button(btn_frame, text="إلغاء", command=payment_window.destroy,
                              bg="#e74c3c", fg="white", font=("Arial", 12, "bold"),
                              width=15, height=2)
        cancel_btn.pack(side="right", padx=10)

    def open_investment_deposits(self):
        """فتح نافذة الودائع الاستثمارية"""
        investment_window = tk.Toplevel(self.root)
        investment_window.title("الودائع الاستثمارية")
        investment_window.geometry("500x400")
        investment_window.configure(bg="#f0f8ff")

        # العنوان
        title_label = tk.Label(investment_window, text="💎 الودائع الاستثمارية",
                              font=("Arial", 16, "bold"),
                              fg="#2c3e50", bg="#f0f8ff")
        title_label.pack(pady=20)

        # عرض أنواع الودائع
        deposits_frame = tk.Frame(investment_window, bg="#ffffff", relief="raised", bd=2)
        deposits_frame.pack(padx=20, pady=10, fill="both", expand=True)

        deposits_info = """
أنواع الودائع الاستثمارية المتاحة:

🔹 وديعة قصيرة الأجل (3 أشهر) - عائد 18% سنوياً
🔹 وديعة متوسطة الأجل (6 أشهر) - عائد 20% سنوياً
🔹 وديعة طويلة الأجل (سنة واحدة) - عائد 22% سنوياً
🔹 وديعة استثمارية (سنتين) - عائد 25% سنوياً
🔹 شهادة استثمار إسلامية - عائد متغير

الحد الأدنى للإيداع: 100,000 جنيه سوداني
يمكن كسر الوديعة مع خصم جزء من العائد
تطبق أحكام الشريعة الإسلامية

للمزيد من المعلومات يرجى زيارة أقرب فرع
        """

        info_label = tk.Label(deposits_frame, text=deposits_info,
                             font=("Arial", 11), bg="#ffffff", justify="right")
        info_label.pack(padx=20, pady=20)

    def open_user_management(self):
        """فتح نافذة إدارة المستخدمين"""
        # التحقق من صلاحيات المستخدم
        if self.current_user and self.users[self.current_user]["role"] != "مدير":
            messagebox.showerror("خطأ", "هذه الخدمة متاحة للمديرين فقط")
            return

        # إذا لم يكن هناك مستخدم مسجل دخول، اطلب تسجيل الدخول
        if not self.current_user:
            if not self.admin_login():
                return

        user_mgmt_window = tk.Toplevel(self.root)
        user_mgmt_window.title("إدارة المستخدمين")
        user_mgmt_window.geometry("600x500")
        user_mgmt_window.configure(bg="#f0f8ff")

        # العنوان
        title_label = tk.Label(user_mgmt_window, text="👥 إدارة المستخدمين",
                              font=("Arial", 16, "bold"),
                              fg="#2c3e50", bg="#f0f8ff")
        title_label.pack(pady=20)

        # إطار الأزرار
        buttons_frame = tk.Frame(user_mgmt_window, bg="#f0f8ff")
        buttons_frame.pack(pady=10)

        # أزرار الإدارة
        tk.Button(buttons_frame, text="عرض المستخدمين",
                 command=lambda: self.show_users(user_mgmt_window),
                 bg="#3498db", fg="white", font=("Arial", 12, "bold"),
                 width=20, height=2).pack(side="left", padx=10)

        tk.Button(buttons_frame, text="إضافة مستخدم جديد",
                 command=lambda: self.add_user(user_mgmt_window),
                 bg="#2ecc71", fg="white", font=("Arial", 12, "bold"),
                 width=20, height=2).pack(side="left", padx=10)

        tk.Button(buttons_frame, text="إدارة الحسابات",
                 command=lambda: self.manage_accounts(user_mgmt_window),
                 bg="#e67e22", fg="white", font=("Arial", 12, "bold"),
                 width=20, height=2).pack(side="left", padx=10)

        # إطار عرض البيانات
        self.data_frame = tk.Frame(user_mgmt_window, bg="#ffffff", relief="raised", bd=2)
        self.data_frame.pack(padx=20, pady=20, fill="both", expand=True)

        # عرض المستخدمين افتراضياً
        self.show_users(user_mgmt_window)

    def admin_login(self):
        """نافذة تسجيل دخول المدير"""
        login_window = tk.Toplevel(self.root)
        login_window.title("تسجيل دخول المدير")
        login_window.geometry("350x250")
        login_window.configure(bg="#f0f8ff")
        login_window.resizable(False, False)

        # جعل النافذة في المقدمة
        login_window.transient(self.root)
        login_window.grab_set()

        # العنوان
        title_label = tk.Label(login_window, text="🔐 تسجيل دخول المدير",
                              font=("Arial", 14, "bold"),
                              fg="#2c3e50", bg="#f0f8ff")
        title_label.pack(pady=20)

        # إطار النموذج
        form_frame = tk.Frame(login_window, bg="#ffffff", relief="raised", bd=2)
        form_frame.pack(padx=20, pady=10, fill="both", expand=True)

        # اسم المستخدم
        tk.Label(form_frame, text="اسم المستخدم:", font=("Arial", 12), bg="#ffffff").pack(pady=(20, 5))
        username_var = tk.StringVar()
        username_entry = tk.Entry(form_frame, textvariable=username_var, width=25, font=("Arial", 11))
        username_entry.pack(pady=5)

        # كلمة المرور
        tk.Label(form_frame, text="كلمة المرور:", font=("Arial", 12), bg="#ffffff").pack(pady=(15, 5))
        password_var = tk.StringVar()
        password_entry = tk.Entry(form_frame, textvariable=password_var, width=25, font=("Arial", 11), show="*")
        password_entry.pack(pady=5)

        login_success = [False]  # استخدام قائمة للتمكن من التعديل

        def authenticate():
            username = username_var.get()
            password = password_var.get()

            if username in self.users and self.users[username]["password"] == password:
                if self.users[username]["role"] == "مدير":
                    self.current_user = username
                    login_success[0] = True
                    messagebox.showinfo("نجح", f"مرحباً {self.users[username]['name']}")
                    login_window.destroy()
                else:
                    messagebox.showerror("خطأ", "ليس لديك صلاحيات إدارية")
            else:
                messagebox.showerror("خطأ", "اسم المستخدم أو كلمة المرور غير صحيحة")

        # أزرار العمليات
        btn_frame = tk.Frame(form_frame, bg="#ffffff")
        btn_frame.pack(pady=20)

        login_btn = tk.Button(btn_frame, text="تسجيل الدخول", command=authenticate,
                             bg="#2ecc71", fg="white", font=("Arial", 12, "bold"),
                             width=12, height=1)
        login_btn.pack(side="left", padx=10)

        cancel_btn = tk.Button(btn_frame, text="إلغاء", command=login_window.destroy,
                              bg="#e74c3c", fg="white", font=("Arial", 12, "bold"),
                              width=12, height=1)
        cancel_btn.pack(side="right", padx=10)

        # انتظار إغلاق النافذة
        login_window.wait_window()
        return login_success[0]

    def show_users(self, parent_window):
        """عرض قائمة المستخدمين"""
        # مسح المحتوى السابق
        for widget in self.data_frame.winfo_children():
            widget.destroy()

        # عنوان القسم
        tk.Label(self.data_frame, text="قائمة المستخدمين",
                font=("Arial", 14, "bold"), bg="#ffffff").pack(pady=10)

        # إنشاء جدول المستخدمين
        columns = ("اسم المستخدم", "الاسم الكامل", "الدور")
        tree = ttk.Treeview(self.data_frame, columns=columns, show="headings", height=10)

        for col in columns:
            tree.heading(col, text=col)
            tree.column(col, width=150, anchor="center")

        # إضافة البيانات
        for username, user_data in self.users.items():
            tree.insert("", "end", values=(username, user_data['name'], user_data['role']))

        tree.pack(fill="both", expand=True, padx=10, pady=10)

    def add_user(self, parent_window):
        """إضافة مستخدم جديد"""
        add_user_window = tk.Toplevel(parent_window)
        add_user_window.title("إضافة مستخدم جديد")
        add_user_window.geometry("400x350")
        add_user_window.configure(bg="#f0f8ff")

        # العنوان
        title_label = tk.Label(add_user_window, text="➕ إضافة مستخدم جديد",
                              font=("Arial", 14, "bold"),
                              fg="#2c3e50", bg="#f0f8ff")
        title_label.pack(pady=20)

        # إطار النموذج
        form_frame = tk.Frame(add_user_window, bg="#ffffff", relief="raised", bd=2)
        form_frame.pack(padx=20, pady=10, fill="both", expand=True)

        # اسم المستخدم
        tk.Label(form_frame, text="اسم المستخدم:", font=("Arial", 12), bg="#ffffff").pack(pady=(20, 5))
        username_var = tk.StringVar()
        username_entry = tk.Entry(form_frame, textvariable=username_var, width=30, font=("Arial", 11))
        username_entry.pack(pady=5)

        # الاسم الكامل
        tk.Label(form_frame, text="الاسم الكامل:", font=("Arial", 12), bg="#ffffff").pack(pady=(15, 5))
        fullname_var = tk.StringVar()
        fullname_entry = tk.Entry(form_frame, textvariable=fullname_var, width=30, font=("Arial", 11))
        fullname_entry.pack(pady=5)

        # كلمة المرور
        tk.Label(form_frame, text="كلمة المرور:", font=("Arial", 12), bg="#ffffff").pack(pady=(15, 5))
        password_var = tk.StringVar()
        password_entry = tk.Entry(form_frame, textvariable=password_var, width=30, font=("Arial", 11), show="*")
        password_entry.pack(pady=5)

        # الدور
        tk.Label(form_frame, text="الدور:", font=("Arial", 12), bg="#ffffff").pack(pady=(15, 5))
        role_var = tk.StringVar()
        role_combo = ttk.Combobox(form_frame, textvariable=role_var,
                                 values=["مدير", "موظف", "عميل"], width=27)
        role_combo.pack(pady=5)

        def save_user():
            username = username_var.get()
            fullname = fullname_var.get()
            password = password_var.get()
            role = role_var.get()

            if not all([username, fullname, password, role]):
                messagebox.showerror("خطأ", "يرجى ملء جميع الحقول")
                return

            if username in self.users:
                messagebox.showerror("خطأ", "اسم المستخدم موجود بالفعل")
                return

            # إضافة المستخدم الجديد
            self.users[username] = {
                "name": fullname,
                "password": password,
                "role": role
            }

            messagebox.showinfo("نجح", f"تم إضافة المستخدم {fullname} بنجاح")
            add_user_window.destroy()
            self.show_users(parent_window)  # تحديث القائمة

        # أزرار العمليات
        btn_frame = tk.Frame(form_frame, bg="#ffffff")
        btn_frame.pack(pady=20)

        save_btn = tk.Button(btn_frame, text="حفظ", command=save_user,
                            bg="#2ecc71", fg="white", font=("Arial", 12, "bold"),
                            width=12, height=2)
        save_btn.pack(side="left", padx=10)

        cancel_btn = tk.Button(btn_frame, text="إلغاء", command=add_user_window.destroy,
                              bg="#e74c3c", fg="white", font=("Arial", 12, "bold"),
                              width=12, height=2)
        cancel_btn.pack(side="right", padx=10)

    def manage_accounts(self, parent_window):
        """إدارة الحسابات المصرفية"""
        # مسح المحتوى السابق
        for widget in self.data_frame.winfo_children():
            widget.destroy()

        # عنوان القسم
        tk.Label(self.data_frame, text="إدارة الحسابات المصرفية",
                font=("Arial", 14, "bold"), bg="#ffffff").pack(pady=10)

        # إطار الأزرار
        btn_frame = tk.Frame(self.data_frame, bg="#ffffff")
        btn_frame.pack(pady=10)

        tk.Button(btn_frame, text="إضافة حساب جديد",
                 command=lambda: self.add_account(parent_window),
                 bg="#2ecc71", fg="white", font=("Arial", 11, "bold"),
                 width=15, height=1).pack(side="left", padx=5)

        tk.Button(btn_frame, text="تجميد حساب",
                 command=lambda: self.freeze_account(),
                 bg="#e74c3c", fg="white", font=("Arial", 11, "bold"),
                 width=15, height=1).pack(side="left", padx=5)

        tk.Button(btn_frame, text="تفعيل حساب",
                 command=lambda: self.activate_account(),
                 bg="#f39c12", fg="white", font=("Arial", 11, "bold"),
                 width=15, height=1).pack(side="left", padx=5)

        # جدول الحسابات
        columns = ("رقم الحساب", "اسم صاحب الحساب", "النوع", "الرصيد", "الهاتف", "الحالة")
        tree = ttk.Treeview(self.data_frame, columns=columns, show="headings", height=8)

        for col in columns:
            tree.heading(col, text=col)
            tree.column(col, width=120, anchor="center")

        # إضافة البيانات
        for account_num, account_data in self.accounts.items():
            tree.insert("", "end", values=(
                account_num,
                account_data['name'],
                account_data['type'],
                f"{account_data['balance']:,.0f}",
                account_data['phone'],
                account_data['status']
            ))

        tree.pack(fill="both", expand=True, padx=10, pady=10)

    def add_account(self, parent_window):
        """إضافة حساب جديد"""
        messagebox.showinfo("إضافة حساب", "سيتم إضافة هذه الميزة قريباً")

    def freeze_account(self):
        """تجميد حساب"""
        messagebox.showinfo("تجميد حساب", "سيتم إضافة هذه الميزة قريباً")

    def activate_account(self):
        """تفعيل حساب"""
        messagebox.showinfo("تفعيل حساب", "سيتم إضافة هذه الميزة قريباً")

    def open_card_management(self):
        """فتح نافذة إدارة البطاقات"""
        card_window = tk.Toplevel(self.root)
        card_window.title("إدارة البطاقات")
        card_window.geometry("400x300")
        card_window.configure(bg="#f0f8ff")

        # العنوان
        title_label = tk.Label(card_window, text="💳 إدارة البطاقات",
                              font=("Arial", 16, "bold"),
                              fg="#2c3e50", bg="#f0f8ff")
        title_label.pack(pady=20)

        # خيارات البطاقات
        options_frame = tk.Frame(card_window, bg="#ffffff", relief="raised", bd=2)
        options_frame.pack(padx=20, pady=10, fill="both", expand=True)

        def block_card():
            messagebox.showinfo("حظر البطاقة", "تم حظر البطاقة بنجاح")

        def unblock_card():
            messagebox.showinfo("إلغاء حظر البطاقة", "تم إلغاء حظر البطاقة بنجاح")

        def change_pin():
            messagebox.showinfo("تغيير الرقم السري", "تم تغيير الرقم السري بنجاح")

        # أزرار الخيارات
        tk.Button(options_frame, text="حظر البطاقة", command=block_card,
                 bg="#e74c3c", fg="white", font=("Arial", 12, "bold"),
                 width=20, height=2).pack(pady=10)

        tk.Button(options_frame, text="إلغاء حظر البطاقة", command=unblock_card,
                 bg="#2ecc71", fg="white", font=("Arial", 12, "bold"),
                 width=20, height=2).pack(pady=10)

        tk.Button(options_frame, text="تغيير الرقم السري", command=change_pin,
                 bg="#3498db", fg="white", font=("Arial", 12, "bold"),
                 width=20, height=2).pack(pady=10)

    def open_login(self):
        """فتح نافذة تسجيل الدخول"""
        messagebox.showinfo("تسجيل الدخول", "أنت مسجل دخول بالفعل")

    def open_operations(self):
        """فتح نافذة العمليات"""
        messagebox.showinfo("العمليات", "عمليات إضافية قريباً...")

    def open_other_services(self):
        """فتح نافذة الخدمات الأخرى"""
        services_window = tk.Toplevel(self.root)
        services_window.title("خدمات أخرى")
        services_window.geometry("500x600")
        services_window.configure(bg="#f0f8ff")

        # العنوان
        title_label = tk.Label(services_window, text="🔧 خدمات أخرى",
                              font=("Arial", 16, "bold"),
                              fg="#2c3e50", bg="#f0f8ff")
        title_label.pack(pady=20)

        # قائمة الخدمات
        services_frame = tk.Frame(services_window, bg="#ffffff", relief="raised", bd=2)
        services_frame.pack(padx=20, pady=10, fill="both", expand=True)

        # خدمات مصرفية متقدمة
        services_list = [
            ("📞 خدمة العملاء", "#3498db", self.customer_service),
            ("🏪 مواقع الفروع", "#2ecc71", self.branch_locations),
            ("💱 أسعار الصرف", "#e74c3c", self.exchange_rates),
            ("📈 الاستثمار والأسهم", "#9b59b6", self.investment_services),
            ("🏠 التمويل العقاري", "#f39c12", self.real_estate_financing),
            ("🚗 تمويل السيارات", "#1abc9c", self.car_financing),
            ("💼 الخدمات التجارية", "#34495e", self.business_services),
            ("🎓 قروض التعليم", "#e67e22", self.education_loans),
            ("💳 طلب بطاقة ائتمان", "#8e44ad", self.credit_card_request),
            ("📊 التقارير المالية", "#16a085", self.financial_reports)
        ]

        for service_name, color, command in services_list:
            btn = tk.Button(services_frame, text=service_name,
                           bg=color, fg="white", font=("Arial", 12, "bold"),
                           width=30, height=2, command=command)
            btn.pack(pady=5, padx=20)

    def customer_service(self):
        """خدمة العملاء"""
        service_window = tk.Toplevel(self.root)
        service_window.title("خدمة العملاء")
        service_window.geometry("450x400")
        service_window.configure(bg="#f0f8ff")

        title_label = tk.Label(service_window, text="📞 خدمة العملاء",
                              font=("Arial", 16, "bold"),
                              fg="#2c3e50", bg="#f0f8ff")
        title_label.pack(pady=20)

        info_frame = tk.Frame(service_window, bg="#ffffff", relief="raised", bd=2)
        info_frame.pack(padx=20, pady=10, fill="both", expand=True)

        contact_info = """
🏦 بنك السودان الإلكتروني
📞 الخط الساخن: 15444
📱 واتساب: +249-91-234-5678
📧 البريد الإلكتروني: <EMAIL>
🌐 الموقع الإلكتروني: www.sudanbank.sd

⏰ ساعات العمل:
السبت - الخميس: 8:00 ص - 8:00 م
الجمعة: 2:00 م - 6:00 م

🎧 خدمة العملاء متاحة 24/7
💬 الدردشة المباشرة متاحة على الموقع
        """

        info_label = tk.Label(info_frame, text=contact_info,
                             font=("Arial", 11), bg="#ffffff", justify="right")
        info_label.pack(padx=20, pady=20)

    def branch_locations(self):
        """مواقع الفروع"""
        branch_window = tk.Toplevel(self.root)
        branch_window.title("مواقع الفروع")
        branch_window.geometry("500x450")
        branch_window.configure(bg="#f0f8ff")

        title_label = tk.Label(branch_window, text="🏪 مواقع الفروع",
                              font=("Arial", 16, "bold"),
                              fg="#2c3e50", bg="#f0f8ff")
        title_label.pack(pady=20)

        branches_frame = tk.Frame(branch_window, bg="#ffffff", relief="raised", bd=2)
        branches_frame.pack(padx=20, pady=10, fill="both", expand=True)

        branches_info = """
🏢 الفروع الرئيسية:

📍 الفرع الرئيسي - الخرطوم
العنوان: شارع الجمهورية، الخرطوم
الهاتف: 0183-123456
ساعات العمل: 8:00 ص - 2:00 م

📍 فرع الخرطوم 2
العنوان: السوق الكبير، الخرطوم 2
الهاتف: 0183-234567
ساعات العمل: 8:00 ص - 2:00 م

📍 فرع أم درمان
العنوان: السوق الشعبي، أم درمان
الهاتف: 0187-345678
ساعات العمل: 8:00 ص - 2:00 م

📍 فرع بحري
العنوان: شارع النيل، بحري
الهاتف: 0185-456789
ساعات العمل: 8:00 ص - 2:00 م

🏧 أجهزة الصراف الآلي: 150+ جهاز
🌍 متوفرة في جميع أنحاء السودان
        """

        branches_label = tk.Label(branches_frame, text=branches_info,
                                 font=("Arial", 10), bg="#ffffff", justify="right")
        branches_label.pack(padx=20, pady=20)

    def exchange_rates(self):
        """أسعار الصرف"""
        rates_window = tk.Toplevel(self.root)
        rates_window.title("أسعار الصرف")
        rates_window.geometry("450x400")
        rates_window.configure(bg="#f0f8ff")

        title_label = tk.Label(rates_window, text="💱 أسعار الصرف",
                              font=("Arial", 16, "bold"),
                              fg="#2c3e50", bg="#f0f8ff")
        title_label.pack(pady=20)

        rates_frame = tk.Frame(rates_window, bg="#ffffff", relief="raised", bd=2)
        rates_frame.pack(padx=20, pady=10, fill="both", expand=True)

        # جدول أسعار الصرف
        columns = ("العملة", "الشراء", "البيع")
        tree = ttk.Treeview(rates_frame, columns=columns, show="headings", height=10)

        for col in columns:
            tree.heading(col, text=col)
            tree.column(col, width=120, anchor="center")

        # أسعار وهمية للعملات
        rates_data = [
            ("دولار أمريكي (USD)", "580.00", "585.00"),
            ("يورو (EUR)", "630.00", "635.00"),
            ("جنيه إسترليني (GBP)", "720.00", "725.00"),
            ("ريال سعودي (SAR)", "154.00", "156.00"),
            ("درهم إماراتي (AED)", "157.00", "159.00"),
            ("جنيه مصري (EGP)", "18.50", "19.00"),
            ("ين ياباني (JPY)", "4.20", "4.30"),
            ("فرنك سويسري (CHF)", "640.00", "645.00")
        ]

        for currency, buy, sell in rates_data:
            tree.insert("", "end", values=(currency, buy, sell))

        tree.pack(fill="both", expand=True, padx=10, pady=10)

        # ملاحظة
        note_label = tk.Label(rates_frame,
                             text=f"آخر تحديث: {datetime.now().strftime('%Y-%m-%d %H:%M')}\nالأسعار قابلة للتغيير",
                             font=("Arial", 9), fg="#7f8c8d", bg="#ffffff")
        note_label.pack(pady=5)

    def investment_services(self):
        """خدمات الاستثمار"""
        messagebox.showinfo("خدمات الاستثمار",
                           "خدمات الاستثمار والأسهم\n"
                           "• صناديق الاستثمار الإسلامية\n"
                           "• تداول الأسهم\n"
                           "• السندات الحكومية\n"
                           "• الاستشارات المالية\n\n"
                           "للمزيد من المعلومات اتصل بـ: 15444")

    def real_estate_financing(self):
        """التمويل العقاري"""
        messagebox.showinfo("التمويل العقاري",
                           "خدمات التمويل العقاري\n"
                           "• تمويل شراء المنازل\n"
                           "• تمويل البناء\n"
                           "• إعادة التمويل\n"
                           "• فترات سداد تصل إلى 25 سنة\n"
                           "• معدلات فائدة تنافسية\n\n"
                           "للاستفسار: 15444")

    def car_financing(self):
        """تمويل السيارات"""
        messagebox.showinfo("تمويل السيارات",
                           "خدمات تمويل السيارات\n"
                           "• تمويل السيارات الجديدة\n"
                           "• تمويل السيارات المستعملة\n"
                           "• فترات سداد مرنة\n"
                           "• إجراءات مبسطة\n"
                           "• موافقة سريعة\n\n"
                           "للاستفسار: 15444")

    def business_services(self):
        """الخدمات التجارية"""
        messagebox.showinfo("الخدمات التجارية",
                           "الخدمات المصرفية للشركات\n"
                           "• حسابات الشركات\n"
                           "• التمويل التجاري\n"
                           "• خطابات الضمان\n"
                           "• الاعتمادات المستندية\n"
                           "• خدمات الرواتب\n\n"
                           "للاستفسار: 15444")

    def education_loans(self):
        """قروض التعليم"""
        messagebox.showinfo("قروض التعليم",
                           "قروض التعليم والدراسة\n"
                           "• تمويل الدراسة الجامعية\n"
                           "• تمويل الدراسات العليا\n"
                           "• تمويل الدراسة بالخارج\n"
                           "• معدلات فائدة مخفضة\n"
                           "• فترة سماح للطلاب\n\n"
                           "للاستفسار: 15444")

    def credit_card_request(self):
        """طلب بطاقة ائتمان"""
        messagebox.showinfo("طلب بطاقة ائتمان",
                           "طلب بطاقة ائتمان\n"
                           "• بطاقات فيزا وماستركارد\n"
                           "• حدود ائتمانية مرنة\n"
                           "• برامج مكافآت\n"
                           "• حماية من الاحتيال\n"
                           "• قبول عالمي\n\n"
                           "للتقديم: 15444")

    def financial_reports(self):
        """التقارير المالية"""
        messagebox.showinfo("التقارير المالية",
                           "التقارير والكشوفات المالية\n"
                           "• كشف حساب شهري\n"
                           "• تقرير المعاملات\n"
                           "• شهادات الرصيد\n"
                           "• التقارير الضريبية\n"
                           "• تقارير مخصصة\n\n"
                           "متاحة عبر الإنترنت والفروع")

    def run(self):
        """تشغيل التطبيق"""
        self.root.mainloop()

# تشغيل التطبيق
if __name__ == "__main__":
    app = BankApp()
    app.run()
