import tkinter as tk
from tkinter import messagebox

# دالة مؤقتة للتعامل مع الضغط على الأزرار
def button_action(name):
    messagebox.showinfo("تنبيه", f"تم اختيار: {name}")

# إنشاء النافذة الرئيسية
root = tk.Tk()
root.title("تطبيق بنك")
root.geometry("400x600")
root.configure(bg="#ffffff")

# إعداد الخط
btn_font = ("Arial", 12, "bold")

# بيانات الأزرار
buttons = [
    ("تحويلات", lambda: button_action("تحويلات")),
    ("دفع فواتير", lambda: button_action("دفع فواتير")),
    ("تفاصيل الحساب", lambda: button_action("تفاصيل الحساب")),
    ("سحب بدون بطاقة", lambda: button_action("سحب بدون بطاقة")),
    ("PAY بنكك", lambda: button_action("PAY بنكك")),
    ("طلب الودائع الاستثمارية", lambda: button_action("طلب الودائع الاستثمارية")),
    ("إدارة المستخدمين", lambda: button_action("إدارة المستخدمين")),
    ("المعاملات السابقة", lambda: button_action("المعاملات السابقة")),
    ("إدارة البطاقات", lambda: button_action("إدارة البطاقات")),
    ("تسجيل", lambda: button_action("تسجيل")),
    ("العمليات", lambda: button_action("العمليات")),
    ("خدمات أخرى", lambda: button_action("خدمات أخرى")),
]

# إنشاء الأزرار في شبكة 4×3
for index, (label, action) in enumerate(buttons):
    row = index // 3
    col = index % 3
    btn = tk.Button(root, text=label, font=btn_font, width=15, height=3, command=action, bg="#d9534f", fg="white")
    btn.grid(row=row, column=col, padx=5, pady=5)

# تشغيل الواجهة
root.mainloop()
